from docx.shared import Pt, RGBColor

from app.core.config import initialize_environment
from app.services.utils.remove_markdown import remove_markdown

parameter_names = [
    "openaikey",
    "main-bucket",
    "redispassword",
    "redishost",
    "ai-model-endpoint",
]
(
    client,
    router,
    s3_client,
    bucket_name,
    model_name,
    redis_password,
    redis_host,
) = initialize_environment(parameter_names)


def set_font_style(
    run,
    font_name="Arial",
    font_size=12,
    font_color=(0, 0, 0),
    bold=False,
    underline=False,
):
    """
    Set the font style of the given run.

    Args:
        run: The run to set the font style of.
        font_name: The name of the font to use. Defaults to Arial.
        font_size: The size of the font to use. Defaults to 12.
        font_color: The color of the font to use. Defaults to black.
        bold: Whether to set the font to bold. Defaults to False.
        underline: Whether to underline the text. Defaults to False.
    """
    run.font.name = font_name
    run.font.size = Pt(font_size)
    run.font.color.rgb = RGBColor(*font_color)
    run.bold = bold
    run.underline = underline


def set_title_style(para, level):
    """
    Set the style of the title based on the level given.

    Args:
        para: The paragraph where the title will be added.
        level: The level of the title. 1 for Heading 1 and 2 for Heading 2.

    """
    run = para.add_run()
    if level == 1:
        set_font_style(
            run, font_size=16, font_color=(0, 0, 139), bold=True, underline=True
        )
    elif level == 2:
        set_font_style(run, font_size=14, font_color=(0, 0, 139), bold=True)


def bold_text(para, text):
    """
    Format the given text into a Word document paragraph, with bold text
    whenever the text is enclosed in double asterisks (e.g. **bold**).

    :param para: The paragraph to add the text to.
    :type para: docx.Paragraph
    :param text: The text to format
    :type text: str
    """
    parts = text.split("**")
    for i, part in enumerate(parts):
        run = para.add_run(part.strip())
        if i % 2 == 1:
            run.bold = True


def format_document_content(document, summary):
    """
    Format the given document content (summary) into a Word document.

    This function takes a document and a summary string, and formats the summary
    into a Word document. It does this by parsing the summary string into lines,
    and then checking each line for certain patterns. If a line matches a pattern,
    it is formatted accordingly. Patterns include:

    - Heading Level 1: lines starting with "# " (and ending with "**")
    - Heading Level 2: lines starting with "## " (and ending with "**")
    - Bullet Points: lines starting with "• "
    - Bold Text: lines containing "**" (bold text patterns like "**text**")

    If a line does not match any of the above patterns, it is added to the
    document as a regular paragraph.

    :param document: The document to format the content into.
    :type document: Document
    :param summary: The content to format into the document.
    :type summary: str
    """
    summary = remove_markdown(summary)
    lines = summary.strip().split("\n")

    for line in lines:
        line = line.strip()

        # Check for Heading Level 1
        if line.startswith("# "):  # and line.endswith("**"):
            para = document.add_heading("", level=1)
            text_to_bold = line[1:].strip()
            bold_text(para, text_to_bold)
            set_title_style(para, 1)

        # Check for Heading Level 2
        elif line.startswith("## "):  # and line.endswith("**"):
            para = document.add_heading("", level=2)
            text_to_bold = line[2:].strip()
            bold_text(para, text_to_bold)
            set_title_style(para, 2)

        # Check for Bullet Points
        elif line.startswith("• "):
            para = document.add_paragraph(style="List Bullet")
            para.paragraph_format.left_indent = Pt(36)
            bold_text(para, line.strip("• ").strip())

        # Check for bold text patterns like "**text**"
        elif "**" in line:
            para = document.add_paragraph()
            bold_text(para, line)

        # Handle all other cases as regular paragraphs
        else:
            document.add_paragraph(line)


business_process_prompt = """You are an expert business analyst tasked with creating a comprehensive report on a business process based on the provided information. Use your extensive knowledge and writing skills to generate a high-quality, well-structured report that covers all the key points in detail. The report should be written in clear, professional language and be engaging to read. Provide thorough explanations for each section, with relevant examples where appropriate. Ensure the report flows logically from one section to the next.

                **Important Note:** Only and strictly follow the provided outline. Include all specified keys and modules mentioned. Do not insert any content outside the given structure. The report should cover each section in the outline comprehensively. Adhere to the exact formatting specified in this prompt.

                **Document Format:**
                • The report should be generated in a well-formatted DOCX file.
                • Adhere strictly to the following outline. Only generate content within this outline.
                • Use bullet points (•) exactly as shown. Do not substitute with asterisks (*) or any other symbols.
                • Follow the provided indentation and formatting precisely.

                "Report outline is below mentioned"
                
                **# [Business Process Document]**
                
                **# [Business Process Name]**
                
                **## Overview:**
                Provide a concise summary of the business process, including its purpose, scope, and importance to the organization. Highlight the key aspects to be covered in the report.
                
                **## Business Process Name:**
                [Business Process Name]
                
                **## Process Objectives:**
                Clearly define the main objectives of the business process and list down all those in bullet points:
                • **Objective 1:** [Description]
                • **Objective 2:** [Description]
                • **Objective 3:** [Description]
                *(...include as many objectives as necessary and remove any not needed)*
                
                **## Stakeholders:**
                Identify the key stakeholders involved in or impacted by the business process and describe their roles and interests, and list down all those in bullet points:
                • **Stakeholder 1:** [Role and Interest]
                • **Stakeholder 2:** [Role and Interest]
                • **Stakeholder 3:** [Role and Interest]
                *(...include as many stakeholders as necessary and remove any not needed)*
                
                **## Key Functionalities:**
                Outline the core functionalities and capabilities of the business process and list down all those in bullet points:
                • **Functionality 1:** [Description]
                • **Functionality 2:** [Description]
                • **Functionality 3:** [Description]
                *(...include as many functionalities as necessary and remove any not needed)*
                
                **## Process Flow:**
                Provide a detailed description of each step in the business process flow. Include all conditional rules or decision points:
                1. **Step 1:** 
                • Task: "[Task Description]"
                • Conditional: [Any relevant condition]
                2. **Step 2:** 
                • Task: "[Task Description]"
                • Conditional: [Any relevant condition]
                3. **Step 3:** 
                • Task: "[Task Description]"
                • Conditional: [Any relevant condition]
                4. *(...continue for all steps and remove any not needed)*
                
                **## Roles and Responsibilities:**
                Summarize the key roles and responsibilities of each stakeholder involved in the business process.
                1. **Role 1:** 
                • Role: "[Role Description]"
                • Responsibility: [Responsibility of the role]
                2. **Role 2:** 
                • Role: "[Role Description]"
                • Responsibility: [Responsibility of the role]
                *(...continue for all roles and responsibilities)*
                
                **Conclusion:**
                Summarize the key findings and recommendations from the report. Highlight the benefits of the business process and opportunities for further improvement.

                **Note:** 
                1. **# ** or **## ** mentioned points in outline should be as it is in generated report.
                2. Never mention ```plaintext**, **```docx and ``` in generated report
                3. Follow the outlined structure meticulously for report generation. The generated document should be well-formatted and adhere to the DOCX format. Cut off any content that does not fit within the specified sections. Only provide the generated report, do not include any additional instructions or content like (```docx,```plaintext,``` etc) and not include also *(...continue for all roles and responsibilities)* this in generated report.
                """
