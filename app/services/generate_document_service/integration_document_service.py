from datetime import datetime

from app.core.config import initialize_environment
from app.services.utils.remove_markdown import remove_markdown

parameter_names = [
    "openaikey",
    "main-bucket",
    "redispassword",
    "redishost",
    "ai-model-endpoint",
]
(
    client,
    router,
    s3_client,
    bucket_name,
    model_name,
    redis_password,
    redis_host,
) = initialize_environment(parameter_names)


def add_table_to_doc(doc, heading, headers, rows):
    """
    Adds a table to a document.

    :param doc: The document to add the table to
    :param heading: The heading for the table
    :param headers: The column headers for the table
    :param rows: The data for the table
    """
    doc.add_heading(heading, level=1)
    table = doc.add_table(rows=1, cols=len(headers))
    table.style = "Table Grid"
    hdr_cells = table.rows[0].cells

    for i, header in enumerate(headers):
        hdr_cells[i].text = header

    for row_data in rows:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)


integration_document_prompt = """**Prompt for Generating a Technical Design Document:**

                            You are an expert in generating Integration documents. Help me generate an introduction document using the data provided by the user. Use your extensive knowledge and writing skills to generate a high-quality, well-structured report that covers all the key points in detail. The report should be written in clear, professional language. You should generate the document using the given data. For any specified sections where data is missing or where your expertise is requested, use your own knowledge base to provide comprehensive and relevant content. Ensure that placeholders and instructional text are not included in the final report.

                            **Instructions:**
                            1. **#** or **##** mentioned points in the outline should appear as-is in the generated report.
                            2. If information is not available in the given data, put "Null" for those fields.
                            3. For these sections: **List of Common Errors**, **Notifications**, **Expected Volume**, **Performance Testing**, **Deployment Steps**, and **Test Cases**, if data is missing, generate detailed and contextually appropriate content using your knowledge base.
                            4. Ensure that all sections are thoroughly and contextually completed based on the provided data and your domain expertise.
                            5. Follow the outlined structure meticulously for document generation. The generated document should be well-formatted and adhere to the DOCX format.
                            6. Do not include these instructions in the generated document.

                            **Report outline is below mentioned:**

                            ### Technical Design Document

                            **Integration Name:** {{name}}
                            **Document generated By:** __(Automatic generation tag)__
                            **Date:** keep it empty, no need to add anything
                            **Tenant Name:** {{tenantId}}
                            **Note:** This document is generated using SimplrOps
                            
                            ### Introduction
                            **Purpose:** 
                            Summarise the purpose of the report in a concise way

                            **Scope:**
                            Summarise the scope of the report in a concise way

                            **Assumptions:**
                            Summarise the assumptions of the report in a concise way
                            
                            **Integration Components:**
                            
                            ### Detailed Design:

                            ### Integration Details
                            Fill in all the information or values if and only if present in the given data; otherwise, just put "Null" as a value:

                            - **Integration System Name:** {{name}}
                            - **Integration Source:** {{sourceName}}
                            - **Integration Destination:** {{targetName}}
                            - **Data Source:** {{dataSource}}
                            - **Integration Type:** {{integrationtype}}
                            - **Transport Method:** {{integrationmethod}}
                            - **Full Extract / Changes Only:** {{changeOnly}}
                            - **Expected Volume:** {{expectedVolume}}
                            - **Run Frequency/Schedule:** {{runFrequency}}
                            - **Integration Notifications:** {{integrationNotification}}

                            ### Data Flow

                            - **Detailed Description:** {{detailedDescription}}

                            ### Security

                            **Integration System User:** {{integrationSystemUser}}

                            **Operation:**
                            - **Domain Security Policy:** {{domainSecurityPolicy}}
                            - **Domain Security Policies Inheriting Permission:** {{domainSecurityPoliciesInheritingPermission}}
                            - **Functional Areas:** {{functionalAreas}}

                            ### Error Handling

                            **List of Common Errors:** 
                            If provided data is missing, generate content based on your expertise:
                            - [Insert common errors identified based on your expertise]

                            **Notifications:**
                            If provided data is missing, generate content based on your expertise:
                            - [Add relevant notifications identified based on your expertise]

                            ### Performance Considerations:

                            **Expected Volume:** 
                            If provided data is missing, generate content based on your expertise:
                            - [Provide expected volume considerations based on typical integrations]

                            **Performance Testing:** 
                            If provided data is missing, generate content based on your expertise:
                            - [Provide performance testing strategies and metrics based on best practices]

                            ### Deployment

                            **Deployment Steps:** 
                            If provided data is missing, generate content based on your expertise:
                            - [Outline deployment steps based on typical processes]

                            **Rollback Plan:** 
                            - Inactivate the Integration

                            ### Maintenance

                            **Monitoring:**
                            - Integration Process Monitoring

                            **Logging:**
                            - Jira Ticketing

                            ### Testing

                            **Test Plan:**
                            - SIT, Regression, E2E, UAT

                            **Test Cases:** 
                            If provided data is missing, generate content based on your expertise:
                            - [List potential test cases based on your expertise]

                            ### Additional Information

                            **Related Documents:** {{relatedDocuments}}

                """


def add_heading(doc, text, level=1):
    doc.add_heading(text, level=level)


def add_bold_heading(doc, text):
    heading = text.strip("**")
    heading_run = doc.add_heading(level=2).add_run(heading)
    heading_run.bold = True


def add_date(doc, line, current_date):
    line = line.replace("**Date:**", f"**Date:** {current_date}")
    add_bold_text(doc, line)


def add_bold_text(doc, line):
    """
    Format the given text into a Word document paragraph, with bold text
    whenever the text is enclosed in double asterisks (e.g. **bold**).

    :param doc: The document to add the paragraph to.
    :type doc: docx.Document
    :param text: The text to format
    :type text: str
    """
    parts = line.split("**")
    p = doc.add_paragraph()
    for i, part in enumerate(parts):
        if i % 2 == 0:
            p.add_run(part)
        else:
            p.add_run(part).bold = True


def add_italic_text(doc, line):
    """
    Format the given text into a Word document paragraph, with italic text
    whenever the text is enclosed in double underscores (e.g. __italic__).

    :param doc: The document to add the paragraph to.
    :type doc: docx.Document
    :param text: The text to format
    :type text: str
    """
    parts = line.split("__")
    p = doc.add_paragraph()
    for i, part in enumerate(parts):
        if i % 2 == 0:
            p.add_run(part)
        else:
            p.add_run(part).italic = True


def add_horizontal_rule(doc):
    doc.add_paragraph("\n")


def process_line(doc, line, current_date):
    """
    Format the given text into a Word document paragraph, using the following rules:

    - Headings: lines starting with "### " are added as headings of level 1
    - Bold headings: lines starting with "**## " are added as bold headings
    - Date: lines containing "**Date:**" are replaced with the current date
    - Bold text: lines containing "**" are added with bold text
    - Italic text: lines containing "__" are added with italic text
    - Horizontal rule: lines starting with "---" are added as horizontal rules
    - Regular text: lines not matching any of the above rules are added as regular paragraphs

    :param doc: The document to add the paragraph to.
    :type doc: docx.Document
    :param line: The line of text to process
    :type line: str
    :param current_date: The current date, to replace "**Date:**" with
    :type current_date: str
    """
    if line.startswith("### "):
        add_heading(doc, line[4:], level=1)
    elif line.startswith("**## "):
        add_bold_heading(doc, line[5:])
    elif "**Date:**" in line:
        add_date(doc, line, current_date)
    elif "**" in line and ":**" in line:
        add_bold_text(doc, line)
    elif line.startswith("**"):
        add_bold_text(doc, line)
    elif line.startswith("__"):
        add_italic_text(doc, line)
    elif line.startswith("---"):
        add_horizontal_rule(doc)
    else:
        doc.add_paragraph(line)


def add_markdown_to_docx(doc, summary):
    summary = remove_markdown(summary)
    current_date = datetime.now().strftime("%Y-%m-%d")
    lines = summary.split("\n")

    doc.add_heading("Technical Design Document", 0)

    for line in lines:
        process_line(doc, line, current_date)


def ensure_string_values(data):
    if isinstance(data, dict):
        return {key: ensure_string_values(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [ensure_string_values(item) for item in data]
    elif not isinstance(data, str):  # Check if the value is not already a string
        return str(data)
    else:
        return data


def extract_data_from_json(data):
    extracted_data = []

    for item in data.get("data", []):
        record = {}

        # Extracting direct keys
        record["sourceName"] = item.get("sourceName", None)
        record["targetName"] = item.get("targetName", None)
        record["changeOnly"] = item.get("changeOnly", None)
        record["name"] = item.get("name", None)
        record["tenantId"] = item.get("tenantId", None)
        record["integrationNotification"] = item.get("integrationNotification", None)

        extracted_data.append(record)

    return extracted_data


def extracted_map_sg_data(data):
    map_sq_data = []

    for item in data.get("data", []):
        record = {
            "securityGroups": extract_security_groups(item),
            "securityDetails": extract_security_details(item),
            "maps": extract_maps(item),
        }
        map_sq_data.append(record)

    return map_sq_data


def extract_security_groups(item):
    security_groups = []
    if isinstance(item.get("securityGroups"), list):
        for sg in item["securityGroups"]:
            if isinstance(sg, dict):
                sg_data = {
                    "securityGroupName": sg.get("securityGroupName", None),
                    "securityGroupType": sg.get("securityGroupType", None),
                    "isInactive": sg.get("isInactive", None),
                    "numberOfMembers": sg.get("numberOfMembers", None),
                }
                security_groups.append(sg_data)
    return security_groups


def extract_security_details(item):
    security_details = []
    if isinstance(item.get("securityDetails"), list):
        for sd in item["securityDetails"]:
            if isinstance(sd, dict):
                sd_data = {"securityAccountName": sd.get("securityAccountName", None)}
                security_details.append(sd_data)
    return security_details


def extract_maps(item):
    maps = []
    if isinstance(item.get("maps"), list):
        for mp in item["maps"]:
            if isinstance(mp, dict):
                mp_data = {
                    "provider": mp.get("provider", None),
                    "name": mp.get("name", None),
                    "mapValues": mp.get("mapValues", []),
                }
                maps.append(mp_data)
    return maps
