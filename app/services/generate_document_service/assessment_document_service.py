from datetime import datetime

from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from pytz import timezone

from app.core.config import initialize_environment
from app.services.utils.remove_markdown import remove_markdown

parameter_names = [
    "openaikey",
    "main-bucket",
    "redispassword",
    "redishost",
    "ai-model-endpoint",
]
(
    client,
    router,
    s3_client,
    bucket_name,
    model_name,
    redis_password,
    redis_host,
) = initialize_environment(parameter_names)

pst = timezone("US/Pacific")
current_date_pst = datetime.now(pst).strftime("%Y-%m-%d %H:%M:%S %Z")

# prompt
assesment_document_prompt = f""" 
                ### Task: Generate a Workday Custom Report-

                You are a Workday expert specializing in generating health checks for Workday custom reports. Utilizing your expertise and the insights provided by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a leader in Workday custom report health checks, generate a detailed and comprehensive health check report based on the given data. The report should be structured and formatted professionally, including the following sections:

                **Report Outline**

                ### Assessment Information:
                **Assessment Name:** Extract from `assessmentName`
                **Assessed by:** SimplrOps
                **Date:**   {current_date_pst}
                **Environment Name:** tenant.displayName otherwise null
                **Items Assessed:** Extract the number from `assessSuccessCount`

                ### Executive Summary:
                Provide an overview highlighting the evaluation of usage, performance, compliance, and governance of custom reports within the Workday system. Emphasize the importance of optimizing these areas for improved system efficiency and compliance.

                ### Overall System Health:
                **Overall Score:** Extract `overAllScore` from `logicScoreStatistics`
                  Discuss the implications of the overall score, detailing the system's performance and potential areas for improvement as analyzed by SimplrOps. Explain how the score was derived and its significance in terms of operational efficiency and report utilization.

                ### Detailed Logic Score Statistics:
                Create a table with columns for Type, Overall, Good, Average, Below Average, and Poor. Populate the table with data from `dimensionDataToShow`, converting the rating counts into percentages for each category (Good, Average, Below Average, Poor). Include an expert analysis that explains the implications of these ratings on business processes and system performance.
                **NOTE:** Put all value in table is in percentage.

                ### Detailed Issues and Recommendations:
                For each identified issue in the JSON data:
                **Issue Title 1:** Extract and format the issue name.
                **Details:** Provide a comprehensive analysis (at least 300 words) of the issue, noting how it affects system usage and performance. Incorporate specific data points and user feedback if available.
                **Impact:** Discuss the severity of the issue in detail, explaining why it is considered moderate, high, or critical, including potential risks to business operations and system integrity.
                **Recommendation:** Offer detailed recommendations (at least 300 words) for resolving the issue, including expected outcomes and benefits. Recommendations should reflect best practices in system management and compliance with organizational standards.

                ### Conclusion:
                Provide a summative analysis of the overall health of Workday custom reports, emphasizing critical areas needing attention and suggesting strategic improvements. Offer insights into how these changes will enhance overall system functionality and user satisfaction.

                **Formatting Instructions:**
                - Clearly separate each section and present it in a structured format for easy navigation and understanding.
                - Use clear, authoritative language to articulate data insights and proposed actions.
                - Establish SimplrOps’s credibility by demonstrating a deep understanding of Workday systems.
                - For formatting "###" and "** **"" only where defined in the prompt outline. No need to add anything from yourside or outside the outline context.
                """


def add_heading(doc, text, level=1):
    """
    Add a heading to the doc.

    Args:
        doc (Document): The Document object to add the heading to.
        text (str): The text of the heading.
        level (int): The level of the heading. Defaults to 1.

    Returns:
        None
    """
    heading = doc.add_heading(text, level=level)
    if level == 1:
        heading.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        heading.runs[0].underline = True


def process_line2(doc, line, in_table, table_data):
    """
    Process a line of text, potentially adding a heading or table to the doc.

    Args:
        doc (Document): The Document object to add the content to.
        line (str): The line of text to process.
        in_table (bool): Whether we are currently inside a table.
        table_data (list): The data for the current table.

    Returns:
        tuple[bool, list]: Whether we are currently inside a table, and the data for the current table.
    """
    if line.startswith("## "):
        add_heading(doc, line.replace("## ", ""), level=2)
    elif line.startswith("### "):
        add_heading(doc, line.replace("### ", ""), level=3)
    elif "**" in line:
        add_bold_paragraph(doc, line)
    elif line.startswith("|"):
        if "---" not in line:
            table_data.append(line.split("|")[1:-1])
            in_table = True
    else:
        if in_table and line.strip() == "":
            add_table(doc, table_data)
            table_data.clear()
            doc.add_paragraph()
            in_table = False
        if line.strip():
            para = doc.add_paragraph(line)
            para.alignment = WD_PARAGRAPH_ALIGNMENT.JUSTIFY
    return in_table, table_data


def add_table(doc, data, style="Table Grid"):
    """
    Add a table to the given document.

    Args:
        doc (Document): The document to add the table to.
        data (list[list[str]]): The data for the table, where each sublist is a row of cells.
        style (str, optional): The name of the table style to use. Defaults to "Table Grid".
    """
    table = doc.add_table(rows=1, cols=len(data[0]))
    table.style = style
    hdr_cells = table.rows[0].cells
    for i, cell in enumerate(data[0]):
        hdr_cells[i].text = cell.strip()
    for row_data in data[1:]:
        row_cells = table.add_row().cells
        for i, cell in enumerate(row_data):
            row_cells[i].text = cell.strip()


def add_bold_paragraph(doc, text):
    """
    Add a paragraph to the given document with bold text.

    Args:
        doc (Document): The document to add the paragraph to.
        text (str): The text to add, with **bold** text wrapped in double asterisks.

    Returns:
        None
    """
    para = doc.add_paragraph()
    para.alignment = WD_PARAGRAPH_ALIGNMENT.JUSTIFY
    parts = text.split("**")
    for i, part in enumerate(parts):
        run = para.add_run(part)
        if i % 2 == 1:
            run.bold = True


def assessment_doc_gen(summary, doc):
    summary = remove_markdown(summary)
    lines = summary.split("\n")
    in_table = False
    table_data = []

    add_heading(doc, "Workday Custom Report", level=1)

    for line in lines:
        in_table, table_data = process_line2(doc, line, in_table, table_data)

    if table_data:
        add_table(doc, table_data)
        doc.add_paragraph()

    return doc
