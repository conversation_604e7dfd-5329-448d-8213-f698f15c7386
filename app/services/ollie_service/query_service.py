from app.core.config import initialize_chain_environment, initialize_environment

parameter_names = [
    "openaikey",
    "main-bucket",
    "redispassword",
    "redishost",
    "ai-model-endpoint",
    "embedding_azure_endpoint",
]
(
    client,
    router,
    s3_client,
    bucket_name,
    model_name,
    redis_password,
    redis_host,
) = initialize_environment(parameter_names)


model = initialize_chain_environment(parameter_names)

query_prompt = """
Create MongoDB aggregate queries based on user requests, adhering to the following guidelines:

### step: MongoDB Aggregate Query Guidelines
1. **Stages Order**: Always use stages in the order: `$group`, `$match`, `$project`.
    - Enclose each stage in curly braces .
    - Use a JSON array `[]` to encapsulate the full query.

2. **Filtering and Ordering**: 
    - Use `$match` for filtering documents.
    - Use `$sort` for ordering results, especially when counting occurrences.
    - Apply descending order for count values by default.

3. **Field Inclusion**:
    - Use `$project` to include specific fields in the output.
    - Always include `simplropsId` and `NAMEFIELD` when listing items, unless a specific field value is requested.
    - Always include the `_id` for Integration Event Detail or Integration Runtime Events models only. For all other queries, remove `_id` from the response by including {_id:0} into the $project query.
    - Always include following condition "{ \"$match\": { \"integration\": { \"$exists\": True } } }" and "{ \"$lookup\": { \"from\": \"integrations\", \"localField\": \"integration\", \"foreignField\": \"_id\", \"as\": \"integration\" } }, { \"$unwind\": { \"path\": \"$integration\", \"preserveNullAndEmptyArrays\": False } }" in a proper order to ensure quick response from database for  Integration Event Detail or integrationRuntimeEvents models
    - Replace the integration name with `NAMEFIELD`, group the integrations after unwinding the query, set the first document as the root to remove duplicates, and add a field called `link` with the value 'DCI' when the user requests the status of a specific integration rather than events like a list of failed or completed integrations. The user can also inquire about the status of the same specific integration within a certain timeframe. 

4. **Field and Array Operations**:
    - For counting array elements, use the `$size` operator.
    - Use `$elemMatch` to specify conditions within array elements.
    - Ensure `$size` expects and accepts only numerical values.

5. **Regex and Case Sensitivity**:
    - Use `$regex` with the `i` option for case-insensitive searches.

6. **Dates and Boolean Values**:
    - Convert given dates to UTC 0 (e.g., `Mar. 21, 2024 10:47 PM PDT` to `2024-03-22T05:47:00.000+00:00`).
    - Use `"False"` for `No` and `"True"` for `Yes` in boolean fields.
    - For date ranges like "last week" or "last month", dynamically calculate the date range from the current date.

7. **Changed/Updated Items**:
    - Include a check for `versionCount > 0` when querying for changed or updated items.
    - For `businessGroups`, use `definitionLastUpdated` as the default field and `lastFunctionallyUpdated` as the default field for item updates.

8. **Specific Mappings**:
    - Map specific prompts to fields:
        - "integrations from" → `sourceName`
        - "integrations to" → `targetName`
        - "template type" → `technologyType`

9. **Strict Formatting**:
    - Ensure the output strictly conforms to JSON.stringify format for MongoDB aggregate queries.
    - Avoid additional instructions or information besides the required MongoDB query.

10. **Model Usage**:
    - Refer to the given model schema ('DBSCHEMA') for field names and structure.
    - Follow the examples provided for structure and style but do not replicate them.

11. **Date usage**:
    - Always use ("START DATE", "CURRENT DATE") (Todays date is 14 august 2024) to use date to generate aggregate queries.
    - However you are generating any query, and in that we have a use of ("START DATE", "CURRENT DATE"), always use this one.
    - Do not use dateSubtract and units to calculate the date instead directly put the calculated dates in query using $gt and $lt operators

12. **Chart Type**:
    - Always generate query which result can easily plot the chart mention in input.

### Task:
Based on the prompt, generate the MongoDB aggregate query according to the guidelines above.

Generate the final output strictly following JSON formatting rules and ensuring that the output is in the JSON.stringify format.

### Examples
Refer to the given examples solely for context when generating queries,if you find question in these examples use thier output as a query. However, avoid directly replicating these examples to provide the same response:

- Example1: Prompt: Which sg are active?, 
Output: [ { "$match": { "isInactive": { "$ne": True } } }, { "$project": { "_id": 0, "simplropsId": 1, "securityGroupName": 1 } } ].

- Example2: Prompt: “Show me the top 5 business processes with most approval counts”,
Output: [ { "$group": { "_id": "$businessProcessDefinition", "approvalCount": { "$sum": { "$size": { "$filter": { "input": "$processSteps", "as": "step", "cond": { "$regexMatch": { "input": "$$step.stepType", "regex": "Approval", "options": "i" } } } } } }, "simplropsId": { "$first": "$simplropsId" } } }, { "$sort": { "approvalCount": -1 } }, { "$limit": 5 }, { "$project": { "simplropsId": 1, "businessProcessDefinition": "$_id", "approvalCount": 1 } } ].

- Example3: Prompt: Give me 5 most recently run custom reports, 
Output: [ { "$sort": { "lastRunDate": -1 } }, { "$limit": 5 }, { "$project": { "_id": 0, "simplropsId": 1, "customReportName": 1, "lastRunDate": 1 } } ].

- Example4: Prompt: What are different integration types are there?, 
Output: [ { "$match": { "technologyType": { "$regex": ".*" } } }, { "$group": { "_id": "$technologyType", "count": { "$sum": 1 } } } , { "$sort": { "count": -1 } } ].

- Example5: Prompt: How many total calc fields are there, 
Output: [ { "$group": { "_id": null, "total": { "$sum": 1 } } } ].

- Example6: Prompt: List of all time offs with type Vacation, 
Output: [ { "$match": { "timeOffType": { "$regex": "Vacation", "$options": "i" } } }, { "$project": { "_id": 0, "simplropsId": 1, "timeOffName": 1 } } ].

- Example7: Prompt: Give me with top 5 security groups that have highest number of domain policies, 
Output: [ { "$sort": { "numberOfPolicies": -1 } }, { "$limit": 5 }, { "$project": { "_id": 0, "securityGroupName": 1, "numberOfPolicies": 1, "simplropsId": 1 } } ].

- Example8: Prompt: List of security groups with report writer access, 
Output: [ { "securityGroupDomains": { "$elemMatch": { "policyName": "Custom Report Creation", "permissions.canModify": True} } }, { "$project": { "_id": 0, "simplropsId": 1, "securityGroupName": 1 } } ].

- Example9: Prompt: Give the number of items in the integration where the PII value is 'No', 
Output: [ { "$match": { "isPII": False } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$sort": { "count": -1 } } ].

- Example10: Prompt: What are the source and target names for the integration named AUTO_Change_Jobv35.0?, 
Output: [ { "$match": { "name": { "$regex": "AUTO_Change_Jobv35.0", "$options": "i" } } }, { "$project": { "_id": 0, "sourceName": "$sourceName", "targetName": "$targetName" } } ].

- Example11: Prompt: Give me list of functional areas for the Dependent Event for Global Support - JAPAC Group business process, 
Output: [ { "$match": { "businessProcessDefinition": {"$regex": "Dependent Event for Global Support - JAPAC Group", "$options": "i"} } }, { "$project": { "_id": 0, "functionalAreas.functionalAreaName": 1 } } ].

- Example12: Prompt: Please check if there are any security group are duplicated or not, 
Output: [ { "$group": { "_id": "$securityGroupName", "count": { "$sum": 1 } } }, { "$match": { "count": { "$gt": 1 } } }, { "$project": { "securityGroupName": "$_id", "count": 1 } } ].

- Example13: Prompt: “list of all business processes”, 
Output: [{ "$project": { "_id": 0, "simplropsId": 1, NAMEFIELD: 1 } }].

- Example14: Prompt: “What are the different types of security groups we have”, 
Output: [ { "$group": { "_id": "$securityGroupType", "count": { "$sum": 1 } } }, { "$match": { "count": { "$gt": 0 } } }, { "$project": { "securityGroupType": "$_id", "count": 1 } }, { "$sort": { "count": -1 } } ].

- Example15: Prompt: “List all the versions available for "Ready for Hire Business Process Ready For Hire (Default Definition)".”, 
Output: [ { "$match": { "businessProcessDefinition": { "$regex": "Accounting Journal Event \\(Default Definition\\)", "$options": "i" } } }, { "$group": { "_id": "$versionCount", "count": { "$sum": 1 } } }, { "$sort": { "count": -1 } }, { "$project": { "_id": 0, "version": "$_id", "count": 1 } } ].

- Example16: Prompt: “Please list all the permissions for "Hire (Default Definition) Business Process" that an HR administrator has access to.”, 
Output: [ { "$match": { "businessProcessDefinition": { "$regex": "Hire \\(Default Definition\\)", "$options": "i" } } }, { "$unwind": "$permissions" }, { "$match": { "permissions": { "$regex": "View all", "$options": "i" } } }, { "$project": { "_id": 0, "simplropsId": 1, "businessProcessDefinition": 1, "permissions": 1 } } ].

- Example17: Prompt: “List some of the business processes with more than 2 steps”, 
Output: [ { "$group": { "_id": "$simplropsId", "stepCount": { "$sum": { "$size": "$processSteps" } }, "businessProcessDefinition": { "$first": "$businessProcessDefinition" } } }, { "$match": { "stepCount": { "$gt": 2 } } }, { "$project": { "_id": 0, "simplropsId": "$_id", "businessProcessDefinition": 1, "stepCount": 1 } } ].

- Example18: Prompt: “Please list all the Business Process Permissions that an HR administrator has access to.”, 
Output: [ { "$match": { "permissions": { "$regex": "View all", "$options": "i" } } }, { "$project": { "_id": 0, "simplropsId": 1, "businessProcessDefinition": 1, "permissions": 1 } } ].

- Example19: Prompt: “Please list all the domain policies that an HR administrator has access to.”, 
Output: [ { "$match": { "securityGroupName": { "$regex": "HR Administrator", "$options": "i" } } }, { "$unwind": "$securityGroupDomains" }, { "$group": { "_id": "$securityGroupDomains.policyName", "count": { "$sum": 1 } } }, { "$sort": { "count": -1 } }, { "$project": { "_id": 0, "policyName": "$_id", "count": 1 } } ].

- Example20: Prompt: “How many tenant Health Assessments have been run so far in SimplrOps for this tenant?”, 
Output: [ { "$match": { "tenantId": `Current tenant id required` } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "count": 1 } } ].

- Example21: Prompt: “How many Release management analyses have been run so far?”, 
Output: [ { "$match": { "tenantId": "CP105_TENANT_ID46" } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "count": 1 } } ].

- Example22: Prompt: “How many Release management analyses have been run in the last year?”, 
Output: [ { "$match": { "tenantId": "CP105_TENANT_ID46", "startTime": { "$gte": ISODate("2023-07-01T00:00:00.000Z"), "$lt": ISODate("2024-07-01T00:00:00.000Z") } } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "count": 1 } } ].

- Example23: Prompt: “How many organizations do not have a supervisory organization?, 
Output: [ { "$match": { "isArchive": { "$ne": True } } }, { "$match": { "superiorOrgWid": { "$exists": True } } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "count": 1 } } ].

- Example24: Prompt: “List some of the interfaces that have more than 2 steps”, 
Output: [ { "$match": { "numberOfProcessSteps": { "$gt": 2 } } } ].

- Example25: Prompt: “How many training centers are located in different places?”, 
Output: [ { "$group": { "_id": "$locale", "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "locale": "$_id", "count": 1 } } ].

- Example25: Prompt: “List of organizations where employee count is more than 2?”, 
Output: [ { "employeeCount": { "$gt": 2 } } ].

- Example27: Prompt: “What is the latest version of integration in SimplrOps?”, 
Output: [ { "$sort": { "api": -1 } }, { "$project": { "apiVersion": 1, "_id": 0 } }, { "$limit": 1 } ].

- Example28: Prompt: “What reports have PII in them?”, 
Output: [ { "$match": { "isPII": True } }, { "$project": { "customReportName": 1, "_id": 0 } } ].

- Example29: Prompt: “How many Domain security Policies does HR Administrator security group have?”, 
Output: [ { "$match": { "securityGroupName": "HR Administrator" } }, { "$project": { "item": 1, "securityGroupDomainsCount": { "$cond": { "if": { "$isArray": "$securityGroupDomains" }, "then": { "$size": "$securityGroupDomains" }, "else": 0 } } } } ].

- Example30: Prompt: “For HR administrator security group, please list all the Domain Policy accesses it has view and modify access to.?”, 
Output: [ { "$match": { "securityGroupName": "HR Administrator" } }, { "$unwind": { "path": "$securityGroupDomains", "preserveNullAndEmptyArrays": True } }, { "$match": { "$and": [ { "securityGroupDomains.permissions.canView": True }, { "securityGroupDomains.permissions.canModify": True } ] } }, { "$project": { "_id": 0, "policyName": "$securityGroupDomains.policyName", "simplropsId": 1 } } ].

- Example31: Prompt: “What are the different types of security groups we have”, 
Output: [  { "$group": { "_id": "$securityGroupType", "count": { "$sum": 1 } } },  { "$match": { "count": { "$gt": 0 } } },  { "$project": { "securityGroupType": "$_id", "count": 1 } }, { "$sort": { "count": -1 } }]”

- Example32: Prompt: “Show me the business process with most custom notifications”, 
Output: [ { "$group": { "_id": "$simplropsId", "notificationCount": { "$sum": { "$size": "$notifications" } }, "businessProcessDefinition": { "$first": "$businessProcessDefinition" }, "simplropsId": { "$first": "$simplropsId" } } }, { "$sort": { "notificationCount": -1 } }, { "$limit": 1 }, { "$project": { "_id":0, "simplropsId": 1, "businessProcessDefinition": 1, "notificationCount": 1 } } ]”

- Example33: Prompt: “How many business processes has approval steps”, 
Output: [ { $group: { _id: "$simplropsId", approvalStepCount: { $sum: { $size: { $filter: { input: "$processSteps", as: "step", cond: { $regexMatch: { input: "$$step.stepType", regex: "Approval", options: "i" } } } } } }, businessProcessDefinition: { $first: "$businessProcessDefinition" } } }, { $match: { approvalStepCount: { $gt: 0 } } }, { "$count": "count" } ]”

- Example34: Prompt: “Show the top 5pp.services.ollie_service.query_se business processes with the highest step counts”, 
Output: [ { "$group": { "_id": "$businessProcessDefinition", "stepCount": { "$sum": { "$size": "$processSteps" } }, "simplropsId": { "$first": "$simplropsId" } } }, { "$sort": { "stepCount": -1 } }, { "$limit": 5 }, { "$project": { "simplropsId": 1, "businessProcessDefinition": "$_id", "stepCount": 1 } }]”

- Example35: Prompt: “Show me failed integration events for the month of MAY 2024”, 
Output: [ { "$match": { "integration": { "$exists": True } } }, { "$match": { "integrationEventStatus": { "$regex": "Failed", "$options": "i" }, "actualStartDateAndTime": { "$gte": "2024-05-01T00:00:00.000Z", "$lt": "2024-06-01T00:00:00.000Z" } } }, { "$lookup": { "from": "integrations", "localField": "integration", "foreignField": "_id", "as": "integration" } }, { "$unwind": { "path": "$integration", "preserveNullAndEmptyArrays": False } }, { "$project": { "_id": 1, "integrationEventRefId": 1 } } ]” 

- Example36: Prompt: “How many business processes were used last month?”, 
Output: [ { "$match": { "processLastUsed": { "$gte": "2024-05-01T00:00:00.000Z", "$lt": "2024-06-01T00:00:00.000Z" } } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "count": 1 } }]” 

- Example19: Prompt: “list of business processes were used last 7 days”, 
Output: [ { "$match": { "processLastUsed": { "$gte": "2024-06-07T00:00:00.000Z", "$lt": "2024-06-14T00:00:00.000Z" } } }, { "$project": { "simplropsId": 1, "businessProcessDefinition": 1 } }]” 

- Example37: Prompt: “list of failed integrations”, 
Output: [ { "$match": { "integrationEventStatus": { "$regex": "Failed", "$options": "i" } } }, { "$lookup": { "from": "integrations", "localField": "integration", "foreignField": "_id", "as": "integration" } }, { "$unwind": { "path": "$integration", "preserveNullAndEmptyArrays": False } }, { "$group": { "_id": "$integration._id", "root": { "$first": "$$ROOT" } } }, { "$replaceRoot": { "newRoot": "$root" } }, { "$addFields": { "integrationEventRefId": "$integration.name", "link": "DCI" } }, { "$project": { "link": 1, "simplropsId": 1, "integrationEventRefId": 1 } } ]” 

- Example38: Prompt: “List different process step types of Add Additional Job (Default Definition) business process?”, 
Output: [ { "$match": { "businessProcessDefinition": { "$regex": "Add Additional Job \\(Default Definition\\)", "$options": "i" } } }, { "$unwind": "$processSteps" }, { "$group": { "_id": "$processSteps.stepType", "count": { "$sum": 1 } } }, { "$sort": { "count": -1 } }, { "$project": { "stepType": "$_id", "count": 1 } }]” 

Example 39: Prompt: “Count of integration not retrieved in digital configuration inventory”, 
Output: [{ $match: { isArchive: { $ne: True } } }, { $match: { isFullConfigRetrieve: { $ne: 'Yes' } } }, { $group: { _id: null, count: { $sum: 1 } } }, { $project: { _id: 0, count: 1 } } ]

Example 40: Prompt: “How many release management analyses have been run so far?”, 
Output: [{ $match: { isArchive: { $ne: True } } }, { $group: { _id: null, count: { $sum: 1 } } }, { $project: { _id: 0, count: 1 } } ]

Example 41: Prompt: “Please list all the business process permissions that HR administrator has access to?”, 
Output: [{ $match: { isArchive: { $ne: True } } }, { $unwind: "$permissions" }, { $group: { _id: "$permissions", count: { $sum: 1 } } }, { $sort: { count: -1 } }, { $project: { permission: "$_id", count: 1 } } ]

Example 42: Prompt: “List of integrations that have expiring schedules in this month?”, 
Output: [{ $match: { isArchive: { $ne: True } } }, { $match: { schedule: { $elemMatch: { nextScheduledDateTime: { $gte: ISODate("2024-08-01T00:00:00.000Z"), $lt: ISODate("2024-09-01T00:00:00.000Z") } } } } }, { $project: { _id: 0, simplropsId: 1, name: 1 } } ]

Example 43: Prompt: “Count of integrations retrieved in DCI”, 
Output: [{ "$match": { "tenantId": "CP105_TENANT_ID46", "isArchive": { "$ne": True } } }, { "$count": "matchingDocuments" } ]

Example 44: Prompt: “List of integrations that have expiring schedules in July?”, 
Output: [{ $match: { isArchive: { $ne: True } } }, { $match: { schedule: { $elemMatch: { nextScheduledDateTime: { $gte: ISODate("2024-08-01T00:00:00.000Z"), $lt: ISODate("2024-09-01T00:00:00.000Z") } } } } }, { $project: { _id: 0, simplropsId: 1, name: 1 } } ]

Example 45: Prompt: “List of condition rules”, 
Output: [{ "$match": { "isArchive": { "$ne": True } } }, { "$project": { "link": 1, "simplropsId": 1, "rule": 1 } } ]

Example 46: Prompt: “List of condition rules in DCI”, 
Output: [{ "$match": { "isArchive": { "$ne": True } } }, { "$project": { "link": 1, "simplropsId": 1, "rule": 1 } } ]

Example 47: Prompt: “What is the permission count for 'implementers' security groups?”, 
Output: [{ "$match": { "isArchive": { "$ne": True } } }, { "$match": { "securityGroupName": { "$regex": "implementers", "$options": "i" } } }, { "$group": { "_id": "$securityGroupName", "permissionsCount": { "$sum": { "$cond": { "if": { "$isArray": "$securityGroupDomains" }, "then": { "$size": "$securityGroupDomains" }, "else": 0 } } }, "simplropsId": { "$first": "$simplropsId" } } }, { "$project": { "_id": 0, "simplropsId": 1, "permissionsCount": 1 } }, { "$group": { "_id": null, "totalPermissionsCount": { "$sum": "$permissionsCount" }, "details": { "$push": { "simplropsId": "$simplropsId", "permissionsCount": "$permissionsCount" } } } }, { "$project": { "_id": 0, "totalPermissionsCount": 1 } } ]

Example 48: Prompt: “Show me top 5 business processes with the highest step counts.”, 
Output: [{ "$match": { "isArchive": { "$ne": True } } }, { "$group": { "_id": "$businessProcessDefinition", "stepCount": { "$sum": { "$size": "$processSteps" } }, "simplropsId": { "$first": "$simplropsId" } } }, { "$sort": { "stepCount": -1 } }, { "$limit": 5 }, { "$project": { "simplropsId": 1, "businessProcessDefinition": "$_id", "stepCount": 1, "_id": 0 } } ]

Example 49: Prompt: "List of scheduled integrations that will run in the next 6 hours."
Output: [{ $match: { isArchive: { $ne: True } } }, { $match: { schedule: { $elemMatch: { nextScheduledDateTime: { $gte: ISODate( ""2024-08-06T00:00:00.000Z"" ), $lt: ISODate( ""2024-08-06T06:00:00.000Z"" ) } } } } }, { $project: { _id: 0, simplropsId: 1, name: 1 } }]

Example 50: Prompt: “Which SG has access to ad Hoc approval for the hire (Default Definition) Business process?”,
Output: [ { $match: {isArchive: { $ne: True } } }, { $lookup: { from: "businessGroups", let: { localBusinessProcessType: "$businessProcessTypesAccess.businessProcessType" }, pipeline: [ { $match: { $expr: { $and: [ { $regexMatch: { input: "$businessProcessDefination", regex: "Hire \(Default Definition\)", options: "i" } }, { $eq: [ "$businessProcessType", "$$localBusinessProcessType" ] } ] } } } ], as: "bp" } }, { $match: { "businessProcessTypesAccess.businessProcessTypesGrantedToSecurityGroupApproveAccess": True } }, { $project: { _id: 0, simplropsId: 1, securityGroupName: 1 } } ]

Generated Output should include:
1. **MongoDB Aggregate Query**: JSON array `[{ }]` containing the required stages (`$group`, `$match`, `$project`).

**Note**: 
    - Ensure the MongoDB query aligns properly with the identified database collection to achieve expected results without additional text.
    - Generate query according to given schema,query_type and chart_type
    - query_type is count then generate query that returns count, if query_type is data then generate query that returns single data, If query_type is list then generate query that returns list of details 
    - Provide only those fields that need to generate asked chart

here is,
**Input Prompt**: {prompt}
**Query Type**: {query_type}
**Mongo schema**: {schema}
**Chart Type**: {chart_type}
"""


def preprocess_query(query):
    query = (
        query.replace("`", "")
        .replace("json", "")
        .replace("html", "")
        .replace("True", "true")
        .replace("False", "false")
    )
    return query


def get_query(instructions, question, query_type, chart_type, schema, history):
    messages = [{"role": "system", "content": instructions}]

    for message in history:
        messages.append({"role": "user", "content": message[0]})
        messages.append({"role": "assistant", "content": message[1]})

    messages.append(
        {
            "role": "user",
            "content": [
                {"type": "text", "text": question},
                {"type": "text", "text": query_type},
                {"type": "text", "text": chart_type},
                {"type": "text", "text": schema},
            ],
        }
    )
    response = client.chat.completions.create(
        model="simplrops-gpt-4o",
        messages=messages,
        temperature=0.2,
        max_tokens=256,
        top_p=0.2,
        frequency_penalty=0,
        presence_penalty=0,
        response_format={"type": "text"},
    )
    return response.choices[0].message.content
