import os
from datetime import datetime

from botocore.exceptions import NoCredentialsError, PartialCredentialsError
from fastapi import HTTPException

from app.core.config import initialize_environment

parameter_names = [
    "openaikey",
    "main-bucket",
    "redispassword",
    "redishost",
    "ai-model-endpoint",
]
(
    client,
    router,
    s3_client,
    bucket_name,
    model_name,
    redis_password,
    redis_host,
) = initialize_environment(parameter_names)


def handle_s3_error(e):
    """Handling S3 exceptions"""
    if isinstance(e, NoCredentialsError):
        raise HTTPException(status_code=500, detail="AWS credentials not found")
    elif isinstance(e, PartialCredentialsError):
        raise HTTPException(
            status_code=500, detail="Incomplete AWS credentials provided"
        )
    else:
        print(f"An error occurred: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")


def save_and_upload_document(doc, file_prefix, folder):
    """Saves the document and uploads it to S3, returning the presigned URL."""
    now = datetime.now()
    file_name = f"{file_prefix}_{int(now.timestamp() * 1000)}.docx"
    file_path = os.path.join(os.getcwd(), file_name)
    doc.save(file_path)

    s3_key = f"CP105/{folder}/{file_name}"
    try:
        s3_client.upload_file(Filename=file_path, Bucket=bucket_name, Key=s3_key)
        print("File uploaded successfully")

        # Generate a presigned URL
        presigned_url = s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": bucket_name, "Key": s3_key},
            ExpiresIn=3600,  # URL expiration time in seconds
        )
        print("Presigned URL generated successfully")
        return presigned_url
    except Exception as e:
        handle_s3_error(e)
    finally:
        os.remove(file_path)
