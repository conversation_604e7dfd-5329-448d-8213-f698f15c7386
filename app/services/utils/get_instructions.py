import logging

from fastapi import HTTPException
from pymongo.errors import PyMongoError

logger = logging.getLogger(__name__)


async def get_instructions(database, use_case_type):
    """
    Retrieves instructions for a given use case from the aiConfiguration collection in the database.

    Args:
        database (motor.motor_asyncio.AsyncIOMotorDatabase): The MongoDB database to query.
        use_case_type (str): The type of use case to get instructions for.

    Returns:
        str: The instructions for the given use case.

    Raises:
        HTTPException: 404 if the use case is not found, 500 if an unexpected error occurs.
    """
    try:
        ai_configuration = database.get_collection("aiConfiguration")

        print(f"aiConfiguration retrieved: {ai_configuration}")
        instructions = await ai_configuration.find_one({"useCaseType": use_case_type})
        logger.debug(f"Instructions retrieved: {instructions}")
        if not instructions or "instructions" not in instructions:
            raise HTTPException(status_code=404, detail="Use case not found")

        return instructions["instructions"]

    except PyMongoError as e:
        logger.error(f"MongoDB error occurred: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")

    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")
