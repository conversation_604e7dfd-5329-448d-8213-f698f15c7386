import json
import os

import redis

from app.core.config import initialize_environment

parameter_names = [
    "openaikey",
    "main-bucket",
    "redispassword",
    "redishost",
    "ai-model-endpoint",
]
(
    client,
    router,
    s3_client,
    bucket_name,
    model_name,
    redis_password,
    redis_host,
) = initialize_environment(parameter_names)

if os.environ.get("redis_host") == "localhost":
    redis_client = redis.Redis(
        charset="utf-8", decode_responses=True, host="localhost", port=6379
    )
    print("Initialized successfully in local")
else:
    redis_client = redis.Redis(
        charset="utf-8", decode_responses=True, host=redis_host, port=6379
    )
    print("Initialized successfully in aws")


def get(key):
    value = redis_client.get(key)
    if value:
        try:
            json_content = json.loads(value)
            return json_content
        except json.JSONDecodeError:
            return None

    else:
        return None


def set(key, value):
    value = json.dumps(value)
    redis_client.set(key, value, ex=1800)


def delete(key):
    redis_client.delete(key)
