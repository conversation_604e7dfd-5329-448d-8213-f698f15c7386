from fastapi import APIRouter, Depends, status

from app.api.v1.controllers.generate_document.assessment_document import (
    router as assessment_document_router,
)
from app.api.v1.controllers.generate_document.business_process_document import (
    router as generate_summary_router,
)
from app.api.v1.controllers.generate_document.integration_document import (
    router as integration_document_router,
)
from app.api.v1.controllers.ollie.classifier import router as ollie_classifier
from app.api.v1.controllers.ollie.query import router as ollie_query
from app.models.schemas import ErrorResponse
from app.services.auth_service import JWTBearer

# Create main router with global configurations
router = APIRouter(
    responses={
        401: {"model": ErrorResponse, "description": "Unauthorized"},
        403: {"model": ErrorResponse, "description": "Forbidden"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"},
    },
)

# JWT auth instance
jwt_auth = JWTBearer()

# Example protected route with proper documentation


@router.get(
    "/protected-route",
    response_model=dict,
    status_code=status.HTTP_200_OK,
    responses={
        200: {
            "description": "Successfully accessed protected route",
            "content": {"application/json": {"example": {"message": "Access granted"}}},
        }
    },
)
async def protected_route(token: str = Depends(jwt_auth)):
    """
    Example protected route that requires JWT authentication.

    Args:
        token: JWT token from the authorization header

    Returns:
        dict: Success message if access is granted

    Raises:
        HTTPException: If token is invalid or missing
    """
    return {"message": "Access granted"}


# Include all routers with proper prefixes and tags
router.include_router(
    generate_summary_router, prefix="/businessprocesses", tags=["business-processes"]
)

router.include_router(
    integration_document_router, prefix="/integration", tags=["integration"]
)

router.include_router(
    assessment_document_router, prefix="/assessment", tags=["assessment"]
)

# Include Ollie routers
router.include_router(ollie_classifier, prefix="/ollie", tags=["ollie"])

router.include_router(ollie_query, prefix="/ollie", tags=["ollie"])
