import json

from docx import Document
from fastapi import Depends, HTTPException, Request

from app.database.database import get_database
from app.services.generate_document_service.integration_document_service import (
    add_markdown_to_docx,
    add_table_to_doc,
    client,
    ensure_string_values,
    extract_data_from_json,
    extracted_map_sg_data,
    model_name,
    router,
)
from app.services.utils.get_instructions import get_instructions
from app.services.utils.save_and_upload_document import save_and_upload_document


@router.post("/integration-documentation")
async def integration_document(request: Request, database=Depends(get_database)):
    """
    Generate a Word document based on the given integration data.

    The function expects a JSON object in the request body that contains the
    integration data. The data is converted to a string format for OpenAI
    and passed to the completion endpoint. The generated text is then used to
    generate a Word document which is saved and uploaded to the specified bucket
    and key. The presigned URL of the uploaded document is returned as a JSON
    response.

    The function will return a 500 error if there are any issues with the request
    or if the OpenAI completion endpoint fails.
    """
    try:
        instructions = await get_instructions(database, "integration_document_prompt")
        data = await request.json()
        extracted_data = extract_data_from_json(data)
        prompt = json.dumps(extracted_data, indent=2)
        messages = [
            {"role": "system", "content": instructions},
            {"role": "user", "content": prompt},
        ]
        summary = await generate_completion(messages)
        doc = create_document(summary, data)
        presigned_url = save_and_upload_document(
            doc, "integration_documentation", "integrationDocuments"
        )
        return {"success": True, "presigned_url": presigned_url}
    except HTTPException as e:
        print(e)
        return e
    except Exception as e:
        print(e)
        raise HTTPException(status_code=500, detail="Internal Server Error")


async def generate_completion(messages):
    """
    Generate completion from OpenAI

    The function takes a list of messages as parameter and posts to the OpenAI
    completion endpoint. The response from the completion endpoint is then
    parsed and the generated text is returned.

    The function is asynchronous.

    :param messages: A list of messages
    :type messages: List[Dict[str, Union[str, int]]]
    :return: The generated text
    :rtype: str
    """
    response = client.chat.completions.create(
        model=model_name, messages=messages, temperature=1, max_tokens=4095, top_p=1
    )
    return response.choices[0].message.content


def create_document(summary, data):
    """
    Create a Word document based on the given integration data

    The function takes two parameters. The first parameter is the summary
    generated by the OpenAI completion endpoint. The second parameter is the
    integration data in JSON format.

    The function creates a Word document and adds the summary and the
    extracted data to the document. The extracted data includes a table of
    maps and a table of security groups.

    The function returns the created document.

    :param summary: The summary generated by the OpenAI completion endpoint
    :type summary: str
    :param data: The integration data in JSON format
    :type data: Dict[str, Union[str, int, List[Dict[str, Union[str, int]]]]]
    :return: The created document
    :rtype: Document
    """

    doc = Document()
    map_sq_data = extracted_map_sg_data(data)
    for data in map_sq_data:
        maps = ensure_string_values(data)
        add_markdown_to_docx(doc, summary)
        add_field_mapping_table(doc, maps["maps"])
        add_security_groups_table(doc, maps["securityGroups"])
    return doc


def add_field_mapping_table(doc, maps):
    """
    Adds a table of field mappings to the given Word document.

    The function takes two parameters. The first parameter is the Word document
    to which the table should be added. The second parameter is a list of maps
    where each map item is a dictionary containing the provider, name, and map
    values of the map. The function extracts the provider, name, internal, and
    external values from each map item and adds them to the Word document as a
    table.

    :param doc: The Word document to which the table should be added
    :type doc: Document
    :param maps: A list of maps where each map item is a dictionary containing
                 the provider, name, and map values of the map
    :type maps: List[Dict[str, Union[str, int, List[Dict[str, Union[str, int]]]]]]
    """
    field_mapping_headers = ["Map Provider", "Map Name", "Internal", "External"]
    field_mapping_rows = [
        (
            map_item["provider"],
            map_item["name"],
            map_value["internal"],
            map_value["external"],
        )
        for map_item in maps
        for map_value in map_item["mapValues"]
    ]
    add_table_to_doc(doc, "Field Mapping", field_mapping_headers, field_mapping_rows)


def add_security_groups_table(doc, security_groups):
    """
    Adds a table of security groups to the given Word document.

    The function takes two parameters. The first parameter is the Word document
    to which the table should be added. The second parameter is a list of
    security groups where each security group is a dictionary containing the
    security group name, type, is inactive, and number of members. The function
    extracts these values from each security group and adds them to the Word
    document as a table.

    :param doc: The Word document to which the table should be added
    :type doc: Document
    :param security_groups: A list of security groups where each security group
                            is a dictionary containing the security group name,
                            type, is inactive, and number of members
    :type security_groups: List[Dict[str, Union[str, int, bool]]]
    """
    security_groups_headers = [
        "Security Group Name",
        "Security Group Type",
        "Is Inactive",
        "Number of Members",
    ]
    security_groups_rows = [
        (
            group["securityGroupName"],
            group["securityGroupType"],
            group["isInactive"],
            group["numberOfMembers"],
        )
        for group in security_groups
    ]
    add_table_to_doc(
        doc, "Security Groups", security_groups_headers, security_groups_rows
    )
