import json

from docx import Document
from fastapi import Depends, HTTPException, Request

from app.database.database import get_database
from app.services.generate_document_service.business_process_document_service import (
    client,
    format_document_content,
    model_name,
    router,
)
from app.services.utils.get_instructions import get_instructions
from app.services.utils.save_and_upload_document import save_and_upload_document


@router.post("/bp-documentation")
async def generate_summary(request: Request, database=Depends(get_database)):
    """
    Generate a Word document based on the given business process data.

    The function expects a JSON object in the request body that contains the
    business process data. The data is converted to a string format for OpenAI
    and passed to the completion endpoint. The generated text is then used to
    generate a Word document which is saved and uploaded to the specified bucket
    and key. The presigned URL of the uploaded document is returned as a JSON
    response.

    The function will return a 500 error if there are any issues with the request
    or if the OpenAI completion endpoint fails.

    :param request: The request object
    :type request: Request
    :param database: The database instance
    :type database: Database
    :return: A JSON object containing the presigned URL of the uploaded document
    :rtype: JSON
    """
    try:
        # Step 0: Get instructions
        instructions = await get_instructions(
            database, "business_process_document_prompt"
        )

        # Step 1: Receive and parse the data
        data = await request.json()

        # Step 2: Convert data to a string format for OpenAI
        prompt = json.dumps(data, indent=2)
        messages = [
            {
                "role": "system",
                "content": instructions,
            },
            {"role": "user", "content": prompt},
        ]

        # Step 3: Generate completion from OpenAI
        response = client.chat.completions.create(
            model=model_name,
            messages=messages,
            temperature=1,
            max_tokens=4095,
            top_p=1,
            frequency_penalty=0,
            presence_penalty=0,
        )

        # Step 4: Extract the generated text
        summary = response.choices[0].message.content

        # Step 5: Create a Word document
        document = Document()
        format_document_content(document, summary)

        # Save, upload document and get presigned URL
        presigned_url = save_and_upload_document(
            document, "bp_documentation", "bpdocuments"
        )

        return {"success": True, "presigned_url": presigned_url}
    except HTTPException as e:
        print(e)
        return e
    except Exception as e:
        print(e)
        raise HTTPException(status_code=500, detail="Internal Server Error")
